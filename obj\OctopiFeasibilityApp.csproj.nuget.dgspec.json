{"format": 1, "restore": {"C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility-program\\OctopiFeasibilityApp\\OctopiFeasibilityApp.csproj": {}}, "projects": {"C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility-program\\OctopiFeasibilityApp\\OctopiFeasibilityApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility-program\\OctopiFeasibilityApp\\OctopiFeasibilityApp.csproj", "projectName": "OctopiFeasibilityApp", "projectPath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility-program\\OctopiFeasibilityApp\\OctopiFeasibilityApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility-program\\OctopiFeasibilityApp\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"CsvHelper": {"target": "Package", "version": "[33.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}