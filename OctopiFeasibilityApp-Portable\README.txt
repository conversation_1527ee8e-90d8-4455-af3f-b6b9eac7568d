Octopi Feasibility App - Portable Version
=========================================

This is a standalone, portable version of the Octopi Feasibility App that can run from a USB drive or any location without requiring .NET installation.

SYSTEM REQUIREMENTS:
- Windows 10 or later (64-bit)
- No additional software installation required

FILES INCLUDED:
- OctopiFeasibilityApp.exe - Main application executable
- octopi.ico - Application icon
- App.config - Configuration file
- OctopiFeasibilityApp.dll.config - Runtime configuration
- README.txt - This file

HOW TO USE:
1. Simply double-click OctopiFeasibilityApp.exe to run the application
2. The app will start immediately without any installation
3. You can copy this entire folder to any location (USB drive, network drive, etc.)

FEATURES:
- Check fiber feasibility for individual addresses
- Batch process multiple addresses from CSV/TXT files
- Export results to CSV format
- FREE address autocomplete using OpenStreetMap (Nominatim & Photon)
- Optional Google Places autocomplete for enhanced suggestions
- Caching of results for improved performance

CONFIGURATION:
- Address autocomplete works immediately with FREE OpenStreetMap services
- OPTIONAL: Add Google API key to OctopiFeasibilityApp.exe.config for enhanced suggestions
- The application includes a hardcoded API key for fiber checking via api.coverage.28east.co.za

ADDRESS AUTOCOMPLETE SERVICES (in order of priority):
1. Nominatim (OpenStreetMap) - FREE, enhanced for street-level addresses
2. Photon (OpenStreetMap) - FREE, South Africa bounding box optimized
3. OpenCage Geocoding - FREE tier (2500 requests/day)
4. Pattern-based suggestions - Built-in South African address patterns
5. Google Places - Optional, requires API key setup

ENHANCED STREET ADDRESS SUPPORT:
- Extracts house numbers, street names, suburbs, and cities from geocoding services
- SMART HOUSE NUMBER GENERATION: Suggests likely house numbers for any street
- Optimized for South African address formats
- Includes common area suggestions (West Acres, Sandton, etc.)
- Fallback pattern matching for local addresses

HOUSE NUMBER FEATURES:
- Type "5 boekenhout" → Get nearby numbers (1, 3, 7, 9, etc.)
- Type "boekenhout street" → Get common numbers (1, 2, 3, 4, 5, etc.)
- Smart even/odd number patterns based on your input
- Pre-generated suggestions for popular streets

TROUBLESHOOTING:
- If the app doesn't start, ensure you're running on Windows 10 or later (64-bit)
- If you get security warnings, right-click the exe and select "Run anyway"
- For network issues, check your internet connection

TROUBLESHOOTING API ERRORS:
- If you get a JSON parsing error, this usually means:
  1. The API endpoint is not available
  2. The API key is invalid or expired
  3. The server is returning an error page instead of JSON
- The app now provides detailed error messages to help diagnose issues
- Check your internet connection and try again

VERSION: 2.0
BUILD DATE: July 11, 2025
CHANGELOG:
- Fixed all nullable reference warnings
- Improved error handling for API responses
- Added better debugging information for API failures
- Updated API URL to use api.coverage.28east.co.za/getfeasibility
- Fixed API request format to use lowercase 'apikey' parameter
- Added fallback to GET request if POST fails
- Added multiple API key parameter formats for compatibility
- Enhanced error messages with request/response details
- Fixed API response parsing to handle multiple providers and service types
- Updated display to show all available FNOs and providers
- Added support for business fiber, home fiber, and standard fiber services
- Fixed JSON parsing to handle complex error objects
- Added FREE address autocomplete using OpenStreetMap (Nominatim & Photon)
- Enhanced geocoding for street-level addresses (house numbers, street names)
- Added OpenCage geocoding service with free tier
- Built-in South African address pattern matching
- SMART HOUSE NUMBER GENERATION: Suggests likely house numbers for any street
- Intelligent even/odd number patterns and nearby number suggestions
- Optimized for local addresses (West Acres, Sandton, etc.)
- Google Maps Places API as optional enhancement (no payment required for basic functionality)
