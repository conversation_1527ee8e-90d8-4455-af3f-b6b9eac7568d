Octopi Feasibility App - Portable Version
=========================================

This is a standalone, portable version of the Octopi Feasibility App that can run from a USB drive or any location without requiring .NET installation.

SYSTEM REQUIREMENTS:
- Windows 10 or later (64-bit)
- No additional software installation required

FILES INCLUDED:
- OctopiFeasibilityApp.exe - Main application executable
- octopi.ico - Application icon
- App.config - Configuration file
- OctopiFeasibilityApp.dll.config - Runtime configuration
- README.txt - This file

HOW TO USE:
1. Simply double-click OctopiFeasibilityApp.exe to run the application
2. The app will start immediately without any installation
3. You can copy this entire folder to any location (USB drive, network drive, etc.)

FEATURES:
- Check fiber feasibility for individual addresses
- Batch process multiple addresses from CSV/TXT files
- Export results to CSV format
- Google Places autocomplete for address suggestions
- Caching of results for improved performance

CONFIGURATION:
- To enable Google Places autocomplete, add your Google API key to App.config
- The application includes a hardcoded API key for fiber checking via api.coverage.28east.co.za

TROUBLESHOOTING:
- If the app doesn't start, ensure you're running on Windows 10 or later (64-bit)
- If you get security warnings, right-click the exe and select "Run anyway"
- For network issues, check your internet connection

TROUBLESHOOTING API ERRORS:
- If you get a JSON parsing error, this usually means:
  1. The API endpoint is not available
  2. The API key is invalid or expired
  3. The server is returning an error page instead of JSON
- The app now provides detailed error messages to help diagnose issues
- Check your internet connection and try again

VERSION: 1.4
BUILD DATE: July 11, 2025
CHANGELOG:
- Fixed all nullable reference warnings
- Improved error handling for API responses
- Added better debugging information for API failures
- Updated API URL to use api.coverage.28east.co.za/getfeasibility
- Fixed API request format to use lowercase 'apikey' parameter
- Updated to match 28east API specification
