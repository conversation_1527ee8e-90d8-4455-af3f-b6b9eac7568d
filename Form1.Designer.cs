﻿namespace OctopiFeasibilityApp;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.pictureBoxLogo = new System.Windows.Forms.PictureBox();
        this.textBoxAddress = new System.Windows.Forms.TextBox();
        this.buttonCheckFNO = new System.Windows.Forms.Button();
        this.comboBoxResults = new System.Windows.Forms.ComboBox();
        this.buttonExportCSV = new System.Windows.Forms.Button();
        this.buttonBatchLoad = new System.Windows.Forms.Button();
        this.progressBarBatch = new System.Windows.Forms.ProgressBar();
        this.SuspendLayout();
        // 
        // pictureBoxLogo
        // 
        this.pictureBoxLogo.Image = System.Drawing.Icon.ExtractAssociatedIcon("octopi.ico").ToBitmap();
        this.pictureBoxLogo.Location = new System.Drawing.Point(20, 20);
        this.pictureBoxLogo.Size = new System.Drawing.Size(48, 48);
        this.pictureBoxLogo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
        // 
        // textBoxAddress
        // 
        this.textBoxAddress.Font = new System.Drawing.Font("Segoe UI", 10F);
        this.textBoxAddress.Location = new System.Drawing.Point(80, 30);
        this.textBoxAddress.Size = new System.Drawing.Size(350, 25);
        this.textBoxAddress.PlaceholderText = "Enter address...";
        // 
        // buttonCheckFNO
        // 
        this.buttonCheckFNO.Font = new System.Drawing.Font("Segoe UI", 10F);
        this.buttonCheckFNO.Location = new System.Drawing.Point(440, 30);
        this.buttonCheckFNO.Size = new System.Drawing.Size(100, 25);
        this.buttonCheckFNO.Text = "Check FNO";
        // 
        // comboBoxResults
        // 
        this.comboBoxResults.Font = new System.Drawing.Font("Segoe UI", 10F);
        this.comboBoxResults.Location = new System.Drawing.Point(80, 70);
        this.comboBoxResults.Size = new System.Drawing.Size(350, 25);
        this.comboBoxResults.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
        // 
        // buttonExportCSV
        // 
        this.buttonExportCSV.Font = new System.Drawing.Font("Segoe UI", 10F);
        this.buttonExportCSV.Location = new System.Drawing.Point(80, 110);
        this.buttonExportCSV.Size = new System.Drawing.Size(120, 30);
        this.buttonExportCSV.Text = "Export CSV";
        // 
        // buttonBatchLoad
        // 
        this.buttonBatchLoad.Font = new System.Drawing.Font("Segoe UI", 10F);
        this.buttonBatchLoad.Location = new System.Drawing.Point(210, 110);
        this.buttonBatchLoad.Size = new System.Drawing.Size(120, 30);
        this.buttonBatchLoad.Text = "Batch Load";
        // 
        // progressBarBatch
        // 
        this.progressBarBatch.Location = new System.Drawing.Point(80, 150);
        this.progressBarBatch.Size = new System.Drawing.Size(350, 20);
        this.progressBarBatch.Style = System.Windows.Forms.ProgressBarStyle.Continuous;
        // 
        // Form1
        // 
        this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 20F);
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(580, 200);
        this.Controls.Add(this.pictureBoxLogo);
        this.Controls.Add(this.textBoxAddress);
        this.Controls.Add(this.buttonCheckFNO);
        this.Controls.Add(this.comboBoxResults);
        this.Controls.Add(this.buttonExportCSV);
        this.Controls.Add(this.buttonBatchLoad);
        this.Controls.Add(this.progressBarBatch);
        this.Font = new System.Drawing.Font("Segoe UI", 10F);
        this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
        this.MaximizeBox = false;
        this.Name = "Form1";
        this.Padding = new System.Windows.Forms.Padding(10);
        this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
        this.Text = "Octopi Fibre Feasibility Checker";
        this.ResumeLayout(false);
        this.PerformLayout();
    }

    #endregion
    private System.Windows.Forms.PictureBox pictureBoxLogo;
    private System.Windows.Forms.TextBox textBoxAddress;
    private System.Windows.Forms.Button buttonCheckFNO;
    private System.Windows.Forms.ComboBox comboBoxResults;
    private System.Windows.Forms.Button buttonExportCSV;
    private System.Windows.Forms.Button buttonBatchLoad;
    private System.Windows.Forms.ProgressBar progressBarBatch;
}
