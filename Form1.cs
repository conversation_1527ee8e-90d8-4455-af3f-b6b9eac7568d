using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Forms;
using CsvHelper;
using System.Globalization;
using System.IO;
using System.Configuration;

namespace OctopiFeasibilityApp
{
    public partial class Form1 : Form
    {
        // Octopi API key (hardcoded)
        private const string OctopiApiKey = "AIzaSyDEzt60rONb-C10rzDYhmK69IB2i16hALs";
        private static readonly HttpClient httpClient = new HttpClient();
        private readonly Dictionary<string, ApiResult> _cache = new();
        private readonly string? _googleApiKey;
        private ListBox? listBoxSuggestions;
        private System.Windows.Forms.Timer? autocompleteTimer;
        private const int AutocompleteDelay = 300; // ms

        public Form1()
        {
            InitializeComponent();
            buttonCheckFNO.Click += ButtonCheckFNO_Click;
            buttonExportCSV.Click += ButtonExportCSV_Click;
            buttonBatchLoad.Click += ButtonBatchLoad_Click;

            _googleApiKey = ConfigurationManager.AppSettings["GoogleApiKey"];

            // Configure HTTP client
            httpClient.DefaultRequestHeaders.Add("User-Agent", "OctopiFeasibilityApp/1.0");
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            InitAutocomplete();
        }

        private void InitAutocomplete()
        {
            listBoxSuggestions = new ListBox
            {
                Visible = false,
                Font = textBoxAddress.Font,
                Width = textBoxAddress.Width,
                Height = 120
            };
            this.Controls.Add(listBoxSuggestions);
            listBoxSuggestions.Left = textBoxAddress.Left;
            listBoxSuggestions.Top = textBoxAddress.Bottom + 2;
            listBoxSuggestions.BringToFront();
            listBoxSuggestions.Click += (s, e) => SelectSuggestion();
            listBoxSuggestions.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    SelectSuggestion();
                    e.Handled = true;
                }
            };
            textBoxAddress.TextChanged += (s, e) =>
            {
                autocompleteTimer?.Stop();
                if (autocompleteTimer == null)
                {
                    autocompleteTimer = new System.Windows.Forms.Timer { Interval = AutocompleteDelay };
                    autocompleteTimer.Tick += async (s2, e2) =>
                    {
                        autocompleteTimer.Stop();
                        await ShowAutocompleteSuggestionsAsync();
                    };
                }
                autocompleteTimer.Start();
            };
            textBoxAddress.KeyDown += (s, e) =>
            {
                if (listBoxSuggestions?.Visible == true)
                {
                    if (e.KeyCode == Keys.Down && listBoxSuggestions.Items.Count > 0)
                    {
                        listBoxSuggestions.Focus();
                        listBoxSuggestions.SelectedIndex = 0;
                        e.Handled = true;
                    }
                }
            };
            textBoxAddress.LostFocus += (s, e) =>
            {
                if (listBoxSuggestions?.Focused != true)
                    if (listBoxSuggestions != null)
                        listBoxSuggestions.Visible = false;
            };
            listBoxSuggestions.LostFocus += (s, e) =>
            {
                if (listBoxSuggestions != null)
                    listBoxSuggestions.Visible = false;
            };
        }

        private void SelectSuggestion()
        {
            if (listBoxSuggestions?.SelectedItem != null)
            {
                textBoxAddress.Text = listBoxSuggestions.SelectedItem.ToString();
                listBoxSuggestions.Visible = false;
                textBoxAddress.Focus();
                textBoxAddress.SelectionStart = textBoxAddress.Text?.Length ?? 0;
            }
        }

        private async Task ShowAutocompleteSuggestionsAsync()
        {
            string input = textBoxAddress.Text.Trim();
            if (string.IsNullOrWhiteSpace(input) || string.IsNullOrWhiteSpace(_googleApiKey) || listBoxSuggestions == null)
            {
                if (listBoxSuggestions != null)
                    listBoxSuggestions.Visible = false;
                return;
            }
            try
            {
                string url = $"https://maps.googleapis.com/maps/api/place/autocomplete/json?input={Uri.EscapeDataString(input)}&types=address&key={_googleApiKey}";
                var response = await httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();
                var json = await response.Content.ReadAsStringAsync();
                var doc = JsonDocument.Parse(json);
                var predictions = doc.RootElement.GetProperty("predictions");
                listBoxSuggestions.Items.Clear();
                foreach (var pred in predictions.EnumerateArray())
                {
                    if (pred.TryGetProperty("description", out var desc))
                    {
                        var description = desc.GetString();
                        if (description != null)
                            listBoxSuggestions.Items.Add(description);
                    }
                }
                if (listBoxSuggestions.Items.Count > 0)
                {
                    listBoxSuggestions.Width = textBoxAddress.Width;
                    listBoxSuggestions.Left = textBoxAddress.Left;
                    listBoxSuggestions.Top = textBoxAddress.Bottom + 2;
                    listBoxSuggestions.Visible = true;
                }
                else
                {
                    listBoxSuggestions.Visible = false;
                }
            }
            catch
            {
                if (listBoxSuggestions != null)
                    listBoxSuggestions.Visible = false;
            }
        }

        private async void ButtonCheckFNO_Click(object? sender, EventArgs e)
        {
            string address = textBoxAddress.Text.Trim();
            if (string.IsNullOrWhiteSpace(address))
            {
                MessageBox.Show("Please enter an address.", "Input Required", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (_cache.TryGetValue(address, out var cachedResult))
            {
                DisplayResult(cachedResult);
                return;
            }

            buttonCheckFNO.Enabled = false;
            comboBoxResults.Items.Clear();
            comboBoxResults.Text = "Checking...";
            try
            {
                var result = await CheckFibreFeasibilityAsync(address);
                _cache[address] = result;
                DisplayResult(result);
            }
            catch (Exception ex)
            {
                comboBoxResults.Items.Clear();
                comboBoxResults.Text = "Error";
                MessageBox.Show($"Failed to check feasibility: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                buttonCheckFNO.Enabled = true;
            }
        }

        private async Task<ApiResult> CheckFibreFeasibilityAsync(string address)
        {
            try
            {
                var requestBody = new { address = address, apiKey = OctopiApiKey };
                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                using var response = await httpClient.PostAsync("https://28east.co.za/api/feasibility", content);
                var responseString = await response.Content.ReadAsStringAsync();

                // Check if response is successful
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"API returned error {response.StatusCode}: {response.ReasonPhrase}. Response: {responseString}");
                }

                // Check if response looks like JSON
                if (string.IsNullOrWhiteSpace(responseString) || responseString.TrimStart().StartsWith("<"))
                {
                    throw new Exception($"API returned HTML instead of JSON. This usually means the API endpoint is not available or the API key is invalid. Response: {responseString.Substring(0, Math.Min(200, responseString.Length))}...");
                }

                var result = JsonSerializer.Deserialize<ApiResult>(responseString, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                if (result == null)
                    throw new Exception("Invalid response from server - JSON deserialization returned null.");

                return result;
            }
            catch (JsonException ex)
            {
                throw new Exception($"Failed to parse API response as JSON: {ex.Message}");
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Network error while contacting API: {ex.Message}");
            }
            catch (TaskCanceledException ex)
            {
                throw new Exception($"Request timed out: {ex.Message}");
            }
        }

        private void DisplayResult(ApiResult result)
        {
            comboBoxResults.Items.Clear();
            if (result.Feasible)
            {
                comboBoxResults.Items.Add($"Fibre Available: Yes | FNO: {result.Fno} | Provider: {result.Provider}");
                if (result.Fno2 != null)
                    comboBoxResults.Items.Add($"FNO2: {result.Fno2}");
                comboBoxResults.SelectedIndex = 0;
            }
            else
            {
                comboBoxResults.Items.Add("Fibre Not Available");
                comboBoxResults.SelectedIndex = 0;
            }
        }

        private void ButtonExportCSV_Click(object? sender, EventArgs e)
        {
            if (_cache.Count == 0)
            {
                MessageBox.Show("No results to export.", "Export", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            using (var sfd = new SaveFileDialog { Filter = "CSV files (*.csv)|*.csv", FileName = "feasibility_log.csv" })
            {
                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        using var writer = new System.IO.StreamWriter(sfd.FileName);
                        using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
                        csv.WriteField("Address");
                        csv.WriteField("Feasible");
                        csv.WriteField("FNO");
                        csv.WriteField("Provider");
                        csv.WriteField("FNO2");
                        csv.NextRecord();
                        foreach (var kvp in _cache)
                        {
                            csv.WriteField(kvp.Key);
                            csv.WriteField(kvp.Value.Feasible);
                            csv.WriteField(kvp.Value.Fno);
                            csv.WriteField(kvp.Value.Provider);
                            csv.WriteField(kvp.Value.Fno2);
                            csv.NextRecord();
                        }
                        MessageBox.Show("Export complete.", "Export", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to export CSV: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private async void ButtonBatchLoad_Click(object? sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Text/CSV files (*.txt;*.csv)|*.txt;*.csv" })
            {
                if (ofd.ShowDialog() != DialogResult.OK)
                    return;
                var addresses = new List<string>();
                try
                {
                    foreach (var line in File.ReadAllLines(ofd.FileName))
                    {
                        var addr = line.Trim();
                        if (!string.IsNullOrWhiteSpace(addr))
                            addresses.Add(addr);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Failed to read file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (addresses.Count == 0)
                {
                    MessageBox.Show("No addresses found in file.", "Batch Load", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                progressBarBatch.Minimum = 0;
                progressBarBatch.Maximum = addresses.Count;
                progressBarBatch.Value = 0;
                buttonBatchLoad.Enabled = false;
                int completed = 0;
                foreach (var address in addresses)
                {
                    if (!_cache.ContainsKey(address))
                    {
                        int retries = 3;
                        while (retries-- > 0)
                        {
                            try
                            {
                                var result = await CheckFibreFeasibilityAsync(address);
                                _cache[address] = result;
                                break;
                            }
                            catch
                            {
                                if (retries == 0)
                                    _cache[address] = new ApiResult { Feasible = false, Fno = "", Provider = "", Fno2 = "" };
                                await Task.Delay(500);
                            }
                        }
                    }
                    completed++;
                    progressBarBatch.Value = completed;
                    progressBarBatch.Refresh();
                }
                buttonBatchLoad.Enabled = true;
                MessageBox.Show($"Batch processing complete. {addresses.Count} addresses processed.", "Batch Load", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private class ApiResult
        {
            public bool Feasible { get; set; }
            public string Fno { get; set; } = string.Empty;
            public string Provider { get; set; } = string.Empty;
            public string? Fno2 { get; set; } // Optional, if present
        }
    }
}
