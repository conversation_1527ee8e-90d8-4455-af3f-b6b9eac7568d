{"version": 2, "dgSpecHash": "KjP6wXEfxWaT0EEbn4ymkJRw+D1muG99dvMy4zAknFS3WromyloL0fFKHXnsFAaHNhlGYmEMMe29UbQh7LaW7w==", "success": true, "projectFilePath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility-program\\OctopiFeasibilityApp\\OctopiFeasibilityApp.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\csvhelper\\33.1.0\\csvhelper.33.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.runtime.win-x64\\6.0.36\\microsoft.netcore.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.runtime.win-x64\\6.0.36\\microsoft.windowsdesktop.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.runtime.win-x64\\6.0.36\\microsoft.aspnetcore.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.crossgen2.win-x64\\6.0.36\\microsoft.netcore.app.crossgen2.win-x64.6.0.36.nupkg.sha512"], "logs": []}